import { 
  calculateNextBillDate, 
  calculateSmartBillDate, 
  getBillingPeriodOptions,
  formatBillingPeriod,
  BillingPeriod 
} from "../app/utils/billDateCalculator"

describe("billDateCalculator", () => {
  describe("calculateSmartBillDate", () => {
    it("should add one month for monthly billing", () => {
      const startDate = new Date("2024-01-15")
      const result = calculateSmartBillDate(startDate, "Monthly")
      
      expect(result.getFullYear()).toBe(2024)
      expect(result.getMonth()).toBe(1) // February (0-indexed)
      expect(result.getDate()).toBe(15)
    })

    it("should add one week for weekly billing", () => {
      const startDate = new Date("2024-01-15")
      const result = calculateSmartBillDate(startDate, "Weekly")
      
      expect(result.getFullYear()).toBe(2024)
      expect(result.getMonth()).toBe(0) // January
      expect(result.getDate()).toBe(22)
    })

    it("should add three months for quarterly billing", () => {
      const startDate = new Date("2024-01-15")
      const result = calculateSmartBillDate(startDate, "Quarterly")
      
      expect(result.getFullYear()).toBe(2024)
      expect(result.getMonth()).toBe(3) // April (0-indexed)
      expect(result.getDate()).toBe(15)
    })

    it("should add one year for annual billing", () => {
      const startDate = new Date("2024-01-15")
      const result = calculateSmartBillDate(startDate, "Annually")
      
      expect(result.getFullYear()).toBe(2025)
      expect(result.getMonth()).toBe(0) // January
      expect(result.getDate()).toBe(15)
    })

    it("should default to monthly for unknown billing period", () => {
      const startDate = new Date("2024-01-15")
      // @ts-ignore - testing invalid input
      const result = calculateSmartBillDate(startDate, "Unknown")
      
      expect(result.getFullYear()).toBe(2024)
      expect(result.getMonth()).toBe(1) // February (0-indexed)
      expect(result.getDate()).toBe(15)
    })
  })

  describe("calculateNextBillDate", () => {
    it("should return future date as-is", () => {
      const futureDate = new Date()
      futureDate.setDate(futureDate.getDate() + 10) // 10 days in future
      
      const result = calculateNextBillDate(futureDate, "Monthly")
      
      expect(result.getTime()).toBe(futureDate.getTime())
    })

    it("should calculate next occurrence for past date", () => {
      const pastDate = new Date()
      pastDate.setMonth(pastDate.getMonth() - 2) // 2 months ago
      
      const result = calculateNextBillDate(pastDate, "Monthly")
      
      // Should be in the future
      expect(result.getTime()).toBeGreaterThan(Date.now())
    })

    it("should handle monthly billing correctly", () => {
      const pastDate = new Date("2024-01-15")
      const today = new Date("2024-03-20")
      
      // Mock current date
      const originalNow = Date.now
      Date.now = jest.fn(() => today.getTime())
      
      const result = calculateNextBillDate(pastDate, "Monthly")
      
      expect(result.getFullYear()).toBe(2024)
      expect(result.getMonth()).toBe(3) // April (0-indexed)
      expect(result.getDate()).toBe(15)
      
      // Restore original Date.now
      Date.now = originalNow
    })
  })

  describe("getBillingPeriodOptions", () => {
    it("should return all billing period options", () => {
      const options = getBillingPeriodOptions()
      
      expect(options).toHaveLength(4)
      expect(options[0]).toEqual({ label: "Weekly", value: "Weekly" })
      expect(options[1]).toEqual({ label: "Monthly", value: "Monthly" })
      expect(options[2]).toEqual({ label: "Quarterly", value: "Quarterly" })
      expect(options[3]).toEqual({ label: "Annually", value: "Annually" })
    })
  })

  describe("formatBillingPeriod", () => {
    it("should format billing periods correctly", () => {
      expect(formatBillingPeriod("Weekly")).toBe("Weekly")
      expect(formatBillingPeriod("Monthly")).toBe("Monthly")
      expect(formatBillingPeriod("Quarterly")).toBe("Every 3 months")
      expect(formatBillingPeriod("Annually")).toBe("Yearly")
    })

    it("should default to Monthly for unknown period", () => {
      // @ts-ignore - testing invalid input
      expect(formatBillingPeriod("Unknown")).toBe("Monthly")
    })
  })
})
