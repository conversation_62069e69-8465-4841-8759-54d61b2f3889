# Manual Add Feature

The Manual Add feature allows users to create new subscriptions when the desired service provider doesn't exist in the system. This is implemented as a multi-step workflow that follows the existing MobX State Tree patterns and design consistency.

## Overview

The feature consists of two main screens:
1. **ManualAddSearchScreen** - Service provider group search with fuzzy matching and confirmation
2. **ManualAddFormScreen** - Comprehensive form for creating the complete service provider and subscription

## User Flow

### Step 1: Service Provider Search & Confirmation
- User searches for a service provider group using fuzzy matching
- System displays top 3 closest existing matches
- If no suitable match exists, user can create a new service provider group
- Confirmation dialog asks: "This service provider group doesn't exist yet. Would you like to create it?"

### Step 2: Minimal Service Provider Setup
- Pre-filled service provider group name from Step 1
- Form collects:
  - Individual merchant/service name within the group
  - Merchant category (dropdown with predefined options)
  - Product/service name and description
  - Pricing tier details (name, cost, currency, billing frequency)
  - Optional trial period
  - User's subscription details (start date, smart bill date calculation)

## Technical Implementation

### Components Created

#### `ConfirmationModal.tsx`
Reusable confirmation dialog component with:
- Customizable title, message, and button text
- Loading state support
- Icon customization
- Consistent styling with existing modals

#### `ManualAddSearchScreen.tsx`
- Fuzzy search using `fast-fuzzy` library
- Top 3 suggestions display
- Create new option when no matches found
- Integration with existing ServiceGroupSearchBar patterns

#### `ManualAddFormScreen.tsx`
- Multi-section form with validation
- Smart bill date calculation based on billing period
- Category and currency dropdowns
- Date pickers with immediate opening
- Loading states and error handling

### Services Created

#### `ManualAddService.ts`
Complete service provider creation with:
- Merchant group creation with category association
- Merchant creation and linking
- Product creation with merchant association
- Pricing tier creation with billing period support
- Comprehensive error handling and Sentry reporting

#### `billDateCalculator.ts`
Utility functions for:
- Smart next bill date calculation
- Billing period handling (Weekly, Monthly, Quarterly, Annually)
- Date manipulation for subscription management

### Navigation Updates

Added new routes to `AppNavigator.tsx`:
- `ManualAddSearch` - Search screen
- `ManualAddForm` - Form screen with service provider group name parameter

### MobX Integration

Leverages existing RootStore methods:
- `addSubscriptionToStore()` - Immediate UI update
- `bustSubscriptionsCache()` - Cache invalidation
- `merchantGroups` - Existing data access

## Database Schema

Creates entities across multiple tables:
- `merchant_groups` - Service provider groups
- `merchant_categories` - Category definitions
- `merchant_group_categories` - Category associations
- `merchants` - Individual service providers
- `products` - Service offerings
- `merchant_products` - Product-merchant associations
- `pricing_tiers` - Pricing and billing information
- `user_subscriptions` - User subscription records

## Error Handling

- Comprehensive validation on form submission
- Sentry error reporting for all failures
- Reactotron logging for development debugging
- User-friendly error messages with Alert dialogs
- Graceful fallbacks for network issues

## Smart Features

### Bill Date Calculation
- Automatically calculates next bill date based on start date and billing period
- Handles edge cases for different billing frequencies
- Updates dynamically when user changes start date or billing period

### Fuzzy Search
- Uses `fast-fuzzy` library for intelligent matching
- Configurable threshold for match quality
- Case-insensitive and symbol-ignoring search
- Top 3 results to avoid overwhelming users

### Form Validation
- Real-time validation feedback
- Required field checking
- Numeric validation for costs and trial periods
- Positive number validation for pricing

## Usage Examples

### Creating a New Netflix-like Service

1. User clicks "Manual Add" from ServiceSelectionScreen
2. Searches for "Netflix" - finds existing Netflix group
3. Can select existing or create "Netflix Custom"
4. Fills form:
   - Merchant: "Netflix Premium US"
   - Category: "Streaming"
   - Product: "Premium HD Plan"
   - Cost: 15.99 EUR, Monthly billing
   - Trial: 7 days
5. System creates complete hierarchy and subscription

### Creating a Completely New Service

1. User searches for "MyCustomApp"
2. No matches found, creates new service provider group
3. Fills complete form with all details
4. System creates entire service provider structure
5. User subscription is created and immediately visible

## Code Examples

### Using the ConfirmationModal
```tsx
<ConfirmationModal
  visible={showConfirmation}
  title="Create New Service Provider Group"
  message={`This service provider group "${searchQuery}" doesn't exist yet. Would you like to create it?`}
  confirmText="Create"
  cancelText="Cancel"
  onConfirm={handleConfirmCreate}
  onCancel={handleCancelCreate}
/>
```

### Smart Bill Date Calculation
```tsx
import { calculateSmartBillDate } from "app/utils/billDateCalculator"

const smartBillDate = calculateSmartBillDate(startDate, "Monthly")
// Automatically adds one month to start date
```

### Manual Service Creation
```tsx
const serviceResult = await manualAddService.createCompleteServiceProvider({
  merchantGroupName: "Netflix",
  merchantName: "Netflix US",
  merchantCategory: "Streaming",
  productName: "Premium Plan",
  cost: 15.99,
  currency: "EUR",
  billingPeriod: "Monthly"
})
```

## Testing

The feature can be tested by:
1. Navigating to Add Subscription screen
2. Clicking "Manual Add" button
3. Searching for non-existent service provider
4. Completing the form with valid data
5. Verifying subscription appears in main list

## Future Enhancements

- Logo upload support for new service providers
- Bulk import from CSV/Excel files
- Service provider suggestions based on user location
- Integration with external service databases
- Advanced pricing tier templates
