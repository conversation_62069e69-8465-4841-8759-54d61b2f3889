import { supabase } from "app/config/config.base"
import { ErrorType, ErrorExperience, reportSentryError } from "app/utils/crashReporting"
import { BillingPeriod } from "app/utils/billDateCalculator"

export interface CreateServiceProviderData {
  merchantGroupName: string
  merchantName: string
  merchantCategory: string
  productName: string
  productDescription?: string
  pricingTierName: string
  cost: number
  currency: string
  billingPeriod: BillingPeriod
  trialPeriodDays?: number
}

export interface ServiceResponse<T> {
  success: boolean
  data?: T
  error?: string
  code?: string
}

export interface CreatedServiceData {
  merchantGroupId: string
  merchantId: string
  productId: string
  pricingTierId: string
}

class ManualAddService {
  /**
   * Create a complete service provider with merchant group, merchant, product, and pricing tier
   */
  public async createCompleteServiceProvider(
    serviceData: CreateServiceProviderData
  ): Promise<ServiceResponse<CreatedServiceData>> {
    try {
      if (__DEV__ && console.tron) {
        console.tron.log('🏗️ Creating complete service provider:', serviceData)
      }

      // Step 1: Create merchant group
      const merchantGroupResult = await this.createMerchantGroup(
        serviceData.merchantGroupName,
        serviceData.merchantCategory
      )
      if (!merchantGroupResult.success || !merchantGroupResult.data) {
        throw new Error(merchantGroupResult.error || 'Failed to create merchant group')
      }

      // Step 2: Create merchant
      const merchantResult = await this.createMerchant(
        serviceData.merchantName,
        merchantGroupResult.data.id
      )
      if (!merchantResult.success || !merchantResult.data) {
        throw new Error(merchantResult.error || 'Failed to create merchant')
      }

      // Step 3: Create product
      const productResult = await this.createProduct(
        serviceData.productName,
        serviceData.productDescription || '',
        merchantResult.data.id
      )
      if (!productResult.success || !productResult.data) {
        throw new Error(productResult.error || 'Failed to create product')
      }

      // Step 4: Create pricing tier
      const pricingTierResult = await this.createPricingTier({
        productId: productResult.data.id,
        name: serviceData.pricingTierName,
        cost: serviceData.cost,
        currency: serviceData.currency,
        billingPeriod: serviceData.billingPeriod,
        trialPeriodDays: serviceData.trialPeriodDays,
      })
      if (!pricingTierResult.success || !pricingTierResult.data) {
        throw new Error(pricingTierResult.error || 'Failed to create pricing tier')
      }

      const result: CreatedServiceData = {
        merchantGroupId: merchantGroupResult.data.id,
        merchantId: merchantResult.data.id,
        productId: productResult.data.id,
        pricingTierId: pricingTierResult.data.id,
      }

      if (__DEV__ && console.tron) {
        console.tron.log('✅ Successfully created complete service provider:', result)
      }

      return { success: true, data: result }
    } catch (error) {
      const serviceError = error instanceof Error ? error : new Error('Failed to create service provider')
      reportSentryError(serviceError, ErrorType.HANDLED, ErrorExperience.DataWrite)

      if (__DEV__ && console.tron) {
        console.tron.error('❌ Failed to create service provider:', error)
      }

      return {
        success: false,
        error: serviceError.message,
        code: 'SERVICE_PROVIDER_CREATE_ERROR'
      }
    }
  }

  /**
   * Create a new merchant group
   */
  private async createMerchantGroup(
    name: string,
    category: string
  ): Promise<ServiceResponse<{ id: string }>> {
    try {
      const { data, error } = await supabase
        .from('merchant_groups')
        .insert([{
          name,
          description: `${name} services`,
          online_only: true,
        }])
        .select('id')
        .single()

      if (error) throw error

      // Add category association
      if (data?.id) {
        await this.addMerchantGroupCategory(data.id, category)
      }

      return { success: true, data: { id: data.id } }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create merchant group'
      }
    }
  }

  /**
   * Add category to merchant group
   */
  private async addMerchantGroupCategory(merchantGroupId: string, categoryName: string) {
    try {
      // First, get or create the category
      let { data: category, error } = await supabase
        .from('merchant_categories')
        .select('id')
        .eq('name', categoryName)
        .single()

      if (error && error.code === 'PGRST116') {
        // Category doesn't exist, create it
        const { data: newCategory, error: createError } = await supabase
          .from('merchant_categories')
          .insert([{ name: categoryName }])
          .select('id')
          .single()

        if (createError) throw createError
        category = newCategory
      } else if (error) {
        throw error
      }

      // Link merchant group to category
      if (category?.id) {
        await supabase
          .from('merchant_group_categories')
          .insert([{
            merchant_group_id: merchantGroupId,
            category_id: category.id
          }])
      }
    } catch (error) {
      if (__DEV__ && console.tron) {
        console.tron.error('❌ Failed to add merchant group category:', error)
      }
    }
  }

  /**
   * Create a new merchant
   */
  private async createMerchant(
    name: string,
    merchantGroupId: string
  ): Promise<ServiceResponse<{ id: string }>> {
    try {
      const { data, error } = await supabase
        .from('merchants')
        .insert([{
          name,
          merchant_group_id: merchantGroupId,
        }])
        .select('id')
        .single()

      if (error) throw error

      return { success: true, data: { id: data.id } }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create merchant'
      }
    }
  }

  /**
   * Create a new product
   */
  private async createProduct(
    name: string,
    description: string,
    merchantId: string
  ): Promise<ServiceResponse<{ id: string }>> {
    try {
      const { data: product, error: productError } = await supabase
        .from('products')
        .insert([{
          name,
          description,
        }])
        .select('id')
        .single()

      if (productError) throw productError

      // Link product to merchant
      const { error: linkError } = await supabase
        .from('merchant_products')
        .insert([{
          merchant_id: merchantId,
          product_id: product.id,
        }])

      if (linkError) throw linkError

      return { success: true, data: { id: product.id } }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create product'
      }
    }
  }

  /**
   * Create a new pricing tier
   */
  private async createPricingTier(data: {
    productId: string
    name: string
    cost: number
    currency: string
    billingPeriod: BillingPeriod
    trialPeriodDays?: number
  }): Promise<ServiceResponse<{ id: string }>> {
    try {
      const { data: pricingTier, error } = await supabase
        .from('pricing_tiers')
        .insert([{
          product_id: data.productId,
          name: data.name,
          cost: data.cost,
          currency: data.currency,
          billing_period: data.billingPeriod,
          trial_period_days: data.trialPeriodDays || null,
          description: `${data.name} plan`,
        }])
        .select('id')
        .single()

      if (error) throw error

      return { success: true, data: { id: pricingTier.id } }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create pricing tier'
      }
    }
  }
}

export const manualAddService = new ManualAddService()
