import React from "react"
import { ImageStyle, View, ViewStyle } from "react-native"
import { AutoImage, Card, Text } from "app/components"
import { colors, spacing } from "app/theme"
import { FontAwesomeIcon } from "@fortawesome/react-native-fontawesome"
import { faChevronRight } from "@fortawesome/free-solid-svg-icons"

export interface ServiceData {
  id: string
  name: string
  description?: string
  cost?: number
  currency?: string
  productId?: string
  merchantGroupLogo?: string
  pricingTierName?: string
}

export interface ServiceSelectionCardProps {
  selectedService: ServiceData | null
  onPress: () => void
  style?: ViewStyle
}

export function ServiceSelectionCard({ 
  selectedService, 
  onPress, 
  style 
}: ServiceSelectionCardProps) {
  if (selectedService) {
    return (
      <Card
        style={[style]}
        onPress={onPress}
        LeftComponent={
          selectedService.merchantGroupLogo ? (
            <AutoImage
              source={{ uri: selectedService.merchantGroupLogo }}
              style={$cardLogo}
            />
          ) : (
            <View style={$logoPlaceholder}>
              <Text style={$logoPlaceholderText}>
                {selectedService.name.charAt(0).toUpperCase()}
              </Text>
            </View>
          )
        }
        content={(
          <View style={$contentContainer}>
            <Text preset="bold">
              {selectedService.name}
            </Text>
            {selectedService.pricingTierName && (
              <Text preset="formHelper">
                {selectedService.pricingTierName}
              </Text>
            )}
            {selectedService.cost && (
              <Text preset="bold" style={$priceText}>
                €{selectedService.cost}
              </Text>
            )}
          </View>
        )}
        RightComponent={
          <FontAwesomeIcon 
            icon={faChevronRight} 
            size={16} 
            color={colors.palette.neutral500} 
          />
        }
      />
    )
  }

  return (
    <Card
      style={[style, {padding: spacing.md}]}
      heading="Service"
      content="Select Product"
      onPress={onPress}
      RightComponent={
      <View style={{justifyContent: 'center'}}>
        <FontAwesomeIcon 
          icon={faChevronRight} 
          size={16} 
          color={colors.palette.neutral500}
        />
      </View>
      }
    />
  )
}

const $contentContainer: ViewStyle = {
  alignSelf:"center"
}

const $cardLogo: ImageStyle = {
  alignSelf:"center",
  width: 40,
  height: 40,
  borderRadius: 8,
}

const $logoPlaceholder: ViewStyle = {
  width: 40,
  height: 40,
  borderRadius: 8,
  backgroundColor: colors.palette.primary500,
  alignItems: 'center',
  justifyContent: 'center',
}

const $logoPlaceholderText = {
  color: colors.palette.neutral100,
  fontSize: 16,
  fontWeight: 'bold' as const,
}

const $priceText = {
  color: colors.palette.primary600,
}
