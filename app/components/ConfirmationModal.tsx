import React, { FC } from "react"
import { Modal, Pressable, TextStyle, View, ViewStyle } from "react-native"
import { Button, Text } from "app/components"
import { FontAwesomeIcon } from "@fortawesome/react-native-fontawesome"
import { faQuestionCircle, faX } from "@fortawesome/free-solid-svg-icons"
import { colors, spacing, typography } from "app/theme"

interface ConfirmationModalProps {
  visible: boolean
  title: string
  message: string
  confirmText?: string
  cancelText?: string
  onCancel: () => void
  onConfirm: () => void
  isLoading?: boolean
  icon?: any
  iconColor?: string
}

export const ConfirmationModal: FC<ConfirmationModalProps> = ({
  visible,
  title,
  message,
  confirmText = "Confirm",
  cancelText = "Cancel",
  onCancel,
  onConfirm,
  isLoading = false,
  icon = faQuestionCircle,
  iconColor = colors.palette.primary500,
}) => {
  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onCancel}
    >
      <View style={$overlay}>
        <View style={$modal}>
          {/* Header */}
          <View style={$header}>
            <View style={$iconContainer}>
              <FontAwesomeIcon 
                icon={icon} 
                color={iconColor} 
                size={24} 
              />
            </View>
            <Pressable style={$closeButton} onPress={onCancel} disabled={isLoading}>
              <FontAwesomeIcon icon={faX} color={colors.textDim} size={16} />
            </Pressable>
          </View>

          {/* Content */}
          <View style={$content}>
            <Text preset="heading" style={$title}>
              {title}
            </Text>
            
            <Text style={$message}>
              {message}
            </Text>
          </View>

          {/* Actions */}
          <View style={$actions}>
            <Button
              text={cancelText}
              preset="default"
              style={$cancelButton}
              onPress={onCancel}
              disabled={isLoading}
            />
            <Button
              text={isLoading ? "Loading..." : confirmText}
              preset="filled"
              style={$confirmButton}
              onPress={onConfirm}
              disabled={isLoading}
            />
          </View>
        </View>
      </View>
    </Modal>
  )
}

const $overlay: ViewStyle = {
  flex: 1,
  backgroundColor: "rgba(0, 0, 0, 0.5)",
  justifyContent: "center",
  alignItems: "center",
  padding: spacing.lg,
}

const $modal: ViewStyle = {
  backgroundColor: colors.background,
  borderRadius: 12,
  width: "100%",
  maxWidth: 400,
  maxHeight: "80%",
}

const $header: ViewStyle = {
  flexDirection: "row",
  justifyContent: "space-between",
  alignItems: "center",
  padding: spacing.lg,
  paddingBottom: spacing.md,
}

const $iconContainer: ViewStyle = {
  width: 40,
  height: 40,
  borderRadius: 20,
  backgroundColor: colors.palette.neutral100,
  justifyContent: "center",
  alignItems: "center",
}

const $closeButton: ViewStyle = {
  width: 32,
  height: 32,
  borderRadius: 16,
  backgroundColor: colors.palette.neutral100,
  justifyContent: "center",
  alignItems: "center",
}

const $content: ViewStyle = {
  paddingHorizontal: spacing.lg,
  paddingBottom: spacing.lg,
}

const $title: TextStyle = {
  fontSize: 20,
  fontFamily: typography.primary.bold,
  color: colors.text,
  marginBottom: spacing.md,
  textAlign: "center",
}

const $message: TextStyle = {
  fontSize: 16,
  fontFamily: typography.primary.normal,
  color: colors.textDim,
  lineHeight: 22,
  textAlign: "center",
}

const $actions: ViewStyle = {
  flexDirection: "row",
  padding: spacing.lg,
  paddingTop: spacing.md,
  gap: spacing.md,
}

const $cancelButton: ViewStyle = {
  flex: 1,
  backgroundColor: colors.palette.neutral200,
}

const $confirmButton: ViewStyle = {
  flex: 1,
}
