import React, { useState, useEffect, useCallback } from "react"
import { View, ViewStyle } from "react-native"
import { observer } from "mobx-react-lite"
import { TextField } from "./TextField"
import { colors, spacing } from "app/theme"
import { FontAwesomeIcon } from "@fortawesome/react-native-fontawesome"
import { faSearch } from "@fortawesome/free-solid-svg-icons"
import { fuzzy } from "fast-fuzzy"

export interface ServiceGroupSearchBarProps {
  /**
   * The data to search through
   */
  data: Array<{ id: string; name: string; description?: string | null }>
  /**
   * Callback when search results change
   */
  onSearchResults: (results: Array<{ id: string; name: string; description?: string | null }>) => void
  /**
   * Placeholder text for the search input
   */
  placeholder?: string
  /**
   * Optional style override for the container
   */
  style?: ViewStyle
}

const SEARCH_OPTIONS = {
  threshold: 0.3, // Lower threshold for more lenient matching
  ignoreCase: true,
  normalizeWhitespace: true,
  ignoreSymbols: true
}

const DEBOUNCE_DELAY = 300 // milliseconds

export const ServiceGroupSearchBar = observer(function ServiceGroupSearchBar({
  data,
  onSearchResults,
  placeholder = "Search service categories...",
  style: $styleOverride
}: ServiceGroupSearchBarProps) {
  const [searchQuery, setSearchQuery] = useState("")

  // Debounced search function
  const performSearch = useCallback((query: string) => {
    if (!query.trim()) {
      onSearchResults(data)
      return
    }

    const results = data.filter(item => {
      const searchText = `${item.name} ${item.description || ""}`.toLowerCase()
      const score = fuzzy(query.toLowerCase(), searchText, SEARCH_OPTIONS)
      return score >= SEARCH_OPTIONS.threshold
    })

    // Sort by relevance (name matches first, then description matches) and limit to 5 results
    const sortedResults = results
      .sort((a, b) => {
        const aNameScore = fuzzy(query.toLowerCase(), a.name.toLowerCase(), SEARCH_OPTIONS)
        const bNameScore = fuzzy(query.toLowerCase(), b.name.toLowerCase(), SEARCH_OPTIONS)
        return bNameScore - aNameScore
      })
      .slice(0, 5) // Limit to 5 results

    onSearchResults(sortedResults)
  }, [data, onSearchResults])

  // Debounce search execution
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      performSearch(searchQuery)
    }, DEBOUNCE_DELAY)

    return () => clearTimeout(timeoutId)
  }, [searchQuery, performSearch])

  const handleSearchChange = (text: string) => {
    setSearchQuery(text)
  }

  const SearchIcon = () => (
    <FontAwesomeIcon 
      icon={faSearch} 
      size={16} 
      color={colors.textDim} 
      style={$searchIconStyle} 
    />
  )

  return (
    <View style={[$containerStyle, $styleOverride]}>
      <TextField
        value={searchQuery}
        onChangeText={handleSearchChange}
        placeholder={placeholder}
        placeholderTextColor={colors.textDim}
        LeftAccessory={SearchIcon}
        containerStyle={$textFieldContainerStyle}
        style={$textFieldStyle}
        inputWrapperStyle={$inputWrapperStyle}
      />
    </View>
  )
})

const $containerStyle: ViewStyle = {
  paddingHorizontal: spacing.sm,
  paddingVertical: spacing.xs,
}

const $textFieldContainerStyle: ViewStyle = {
  marginBottom: 0,
}

const $textFieldStyle: ViewStyle = {
  fontSize: 16,
  paddingVertical: 0,
  textAlignVertical: 'center',
}

const $inputWrapperStyle: ViewStyle = {
  backgroundColor: colors.palette.neutral100,
  borderRadius: 8,
  borderWidth: 1,
  borderColor: colors.border,
  paddingHorizontal: spacing.md,
  minHeight: 48,
  alignItems: 'center',
}

const $searchIconStyle: ViewStyle = {
  marginRight: spacing.sm,
  alignSelf: 'center',
}
