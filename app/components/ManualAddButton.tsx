import React from "react"
import { Pressable, PressableStateCallbackType, TextStyle, ViewStyle } from "react-native"
import { observer } from "mobx-react-lite"
import { Text } from "./Text"
import { colors, spacing, typography } from "app/theme"
import { FontAwesomeIcon } from "@fortawesome/react-native-fontawesome"
import { faPlus } from "@fortawesome/free-solid-svg-icons"

export interface ManualAddButtonProps {
  /**
   * Text to display on the button
   */
  text?: string
  /**
   * Callback when button is pressed (optional for now)
   */
  onPress?: () => void
  /**
   * Whether the button is disabled
   */
  disabled?: boolean
  /**
   * Optional style override for the button container
   */
  style?: ViewStyle
  /**
   * Optional style override for the button text
   */
  textStyle?: TextStyle
}

export const ManualAddButton = observer(function ManualAddButton({
  text = "Manual Add",
  onPress,
  disabled = false,
  style: $styleOverride,
  textStyle: $textStyleOverride
}: ManualAddButtonProps) {
  
  const $viewStyle = ({ pressed }: PressableStateCallbackType): ViewStyle => [
    $baseButtonStyle,
    disabled && $disabledButtonStyle,
    pressed && $pressedButtonStyle,
    $styleOverride,
  ]

  const $textStyle: TextStyle = [
    $baseTextStyle,
    disabled && $disabledTextStyle,
    $textStyleOverride,
  ]

  return (
    <Pressable
      style={$viewStyle}
      onPress={onPress}
      disabled={disabled}
      accessibilityRole="button"
      accessibilityState={{ disabled }}
      accessibilityLabel={text}
    >
      <FontAwesomeIcon 
        icon={faPlus} 
        size={14} 
        color={disabled ? colors.textDim : colors.palette.primary600} 
        style={$iconStyle} 
      />
      <Text style={$textStyle}>{text}</Text>
    </Pressable>
  )
})

const $baseButtonStyle: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  justifyContent: "center",
  backgroundColor: colors.palette.neutral100,
  borderWidth: 1,
  borderColor: colors.palette.primary600,
  borderRadius: 8,
  paddingVertical: spacing.sm,
  paddingHorizontal: spacing.md,
  minHeight: 48,
  shadowColor: colors.palette.neutral900,
  shadowOffset: { width: 0, height: 1 },
  shadowOpacity: 0.1,
  shadowRadius: 2,
  elevation: 2,
}

const $pressedButtonStyle: ViewStyle = {
  backgroundColor: colors.palette.primary100,
  transform: [{ scale: 0.98 }],
}

const $disabledButtonStyle: ViewStyle = {
  backgroundColor: colors.palette.neutral200,
  borderColor: colors.border,
  shadowOpacity: 0,
  elevation: 0,
}

const $baseTextStyle: TextStyle = {
  fontSize: 14,
  fontFamily: typography.primary.semiBold,
  color: colors.palette.primary600,
  textAlign: "center",
}

const $disabledTextStyle: TextStyle = {
  color: colors.textDim,
}

const $iconStyle: ViewStyle = {
  marginRight: spacing.xs,
  alignSelf: 'center',
}
