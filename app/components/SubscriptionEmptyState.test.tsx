import { fireEvent, render } from "@testing-library/react-native"
import React from "react"

import { SubscriptionEmptyState } from "./SubscriptionEmptyState"

describe("SubscriptionEmptyState", () => {
  const mockOnAddSubscription = jest.fn()

  beforeEach(() => {
    jest.clearAllMocks()
  })

  it("should render the component with correct text", () => {
    const { getByText } = render(
      <SubscriptionEmptyState onAddSubscription={mockOnAddSubscription} />,
    )

    expect(getByText("Add your first subscription")).toBeDefined()
    expect(
      getByText("Start tracking your subscriptions to stay on top of your recurring expenses"),
    ).toBeDefined()
    expect(getByText("Add Subscription")).toBeDefined()
  })

  it("should call onAddSubscription when button is pressed", () => {
    const { getByText } = render(
      <SubscriptionEmptyState onAddSubscription={mockOnAddSubscription} />,
    )

    const addButton = getByText("Add Subscription")
    fireEvent.press(addButton)

    expect(mockOnAddSubscription).toHaveBeenCalledTimes(1)
  })

  it("should render with custom style", () => {
    const customStyle = { backgroundColor: "red" }
    const { getByTestId } = render(
      <SubscriptionEmptyState onAddSubscription={mockOnAddSubscription} style={customStyle} />,
    )

    // Note: We would need to add testID to the component to test this properly
    // For now, just ensure it renders without crashing
    expect(getByText("Add your first subscription")).toBeDefined()
  })
})
