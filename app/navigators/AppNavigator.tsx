/**
 * The app navigator (formerly "AppNavigator" and "MainNavigator") is used for the primary
 * navigation flows of your app.
 * Generally speaking, it will contain an auth flow (registration, login, forgot password)
 * and a "main" flow which the user will use once logged in.
 */
// NavigationContainer is now handled in app.tsx
import { createNativeStackNavigator, NativeStackScreenProps } from "@react-navigation/native-stack"
import { observer } from "mobx-react-lite"
import React from "react"
// useColorScheme is now handled in app.tsx
import * as Screens from "app/screens"
import Config from "../config"
import { useBackButtonHandler } from "./navigationUtilities"
import { TabNavigator } from "./TabNavigator"


/**
 * This type allows TypeScript to know what routes are defined in this navigator
 * as well as what properties (if any) they might take when navigating to them.
 *
 * If no params are allowed, pass through `undefined`. Generally speaking, we
 * recommend using your MobX-State-Tree store(s) to keep application state
 * rather than passing state through navigation params.
 *
 * For more information, see this documentation:
 *   https://reactnavigation.org/docs/params/
 *   https://reactnavigation.org/docs/typescript#type-checking-the-navigator
 *   https://reactnavigation.org/docs/typescript/#organizing-types
 */
export type AppStackParamList = {
  Welcome: undefined
  Login: undefined
	HouseholdProfile: undefined
	ProfileQuestionModal: undefined
	UserProfile: { userId: string }
	Merchant: undefined
	Product: undefined
  AddMember: undefined
  AddSubscription: undefined
  ServiceSelection: { onSelectService: (service: string) => void }
  Home: undefined
  MagicLink: undefined
  MagicAuth: undefined
  EmailAuth: undefined
  AuthenticationGuard: undefined
  Authentication: undefined
  OneSubOnboarding: undefined
	// IGNITE_GENERATOR_ANCHOR_APP_STACK_PARAM_LIST
}

/**
 * This is a list of all the route names that will exit the app if the back button
 * is pressed while in that screen. Only affects Android.
 */
const exitRoutes = Config.exitRoutes

export type AppStackScreenProps<T extends keyof AppStackParamList> = NativeStackScreenProps<
  AppStackParamList,
  T
>

// Documentation: https://reactnavigation.org/docs/stack-navigator/
const Stack = createNativeStackNavigator<AppStackParamList>()

const MainStack = observer(function MainStack() {

  return (
    <Stack.Navigator
      initialRouteName="Home" // Start with the tab navigator
    >
      {/* Welcome screen is now part of the TabNavigator */}
      <Stack.Screen name="HouseholdProfile" component={Screens.HouseholdProfileScreen} />
      <Stack.Screen name="UserProfile" component={Screens.UserProfileScreen}
        options={({route})=>{
          userId: route.params?.userId
        }}
      />

      <Stack.Screen name="ProfileQuestionModal" component={Screens.ProfileQuestionModalScreen}
        options={{
          presentation: "modal",
          headerShown: false,
        }}
      />
      <Stack.Screen name="Merchant" component={Screens.MerchantScreen} />
      <Stack.Screen name="Product" component={Screens.ProductScreen} />
      <Stack.Screen name={"Authentication"} component={Screens.AuthenticationScreen} />
      <Stack.Screen
        name="AddMember"
        component={Screens.AddMemberScreen}
        options={{
          title: "Add Member",
        }}
      />
      <Stack.Screen
        name="AddSubscription"
        component={Screens.AddSubscriptionScreen}
        options={{
          title: "Add Subscription",
        }}
      />
      <Stack.Screen
        name="Home"
        component={TabNavigator}
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen name="ServiceSelection" component={Screens.ServiceSelectionScreen}
                    options={{
                      title: "Add Service",
                    }}
      />
      <Stack.Screen
        name="OneSubOnboarding"
        component={Screens.OneSubOnboarding}
        options={{
          headerShown: false,
        }}
      />
      {/* IGNITE_GENERATOR_ANCHOR_STACK_NAVIGATOR */}
    </Stack.Navigator>
  )
})

export interface NavigationProps {}

export const AppNavigator = observer(function AppNavigator(props: NavigationProps) {
  useBackButtonHandler((routeName) => exitRoutes.includes(routeName))

  return <MainStack />
})
