/**
 * Utility functions for calculating smart bill dates based on billing periods
 */

export type BillingPeriod = "Weekly" | "Monthly" | "Quarterly" | "Annually"

/**
 * Calculate the next bill date based on the initial bill date and billing period
 * If the initial bill date has passed, calculate the next occurrence
 * @param initialBillDate - The initial bill date
 * @param billingPeriod - The billing period frequency
 * @returns The next bill date as a Date object
 */
export const calculateNextBillDate = (
  initialBillDate: Date,
  billingPeriod: BillingPeriod = "Monthly"
): Date => {
  const today = new Date()
  const billDate = new Date(initialBillDate)

  // If the initial bill date is in the future, return it
  if (billDate > today) {
    return billDate
  }

  // Calculate the next bill date based on billing period
  let nextDate = new Date(billDate)

  while (nextDate <= today) {
    switch (billingPeriod) {
      case "Weekly":
        nextDate.setDate(nextDate.getDate() + 7)
        break
      case "Monthly":
        nextDate.setMonth(nextDate.getMonth() + 1)
        break
      case "Quarterly":
        nextDate.setMonth(nextDate.getMonth() + 3)
        break
      case "Annually":
        nextDate.setFullYear(nextDate.getFullYear() + 1)
        break
      default:
        // Default to monthly if unknown period
        nextDate.setMonth(nextDate.getMonth() + 1)
        break
    }
  }

  return nextDate
}

/**
 * Calculate a smart bill date based on start date and billing period
 * This is used when creating a new subscription
 * @param startDate - The subscription start date
 * @param billingPeriod - The billing period frequency
 * @returns The calculated bill date as a Date object
 */
export const calculateSmartBillDate = (
  startDate: Date,
  billingPeriod: BillingPeriod = "Monthly"
): Date => {
  const billDate = new Date(startDate)

  // Add one billing period to the start date
  switch (billingPeriod) {
    case "Weekly":
      billDate.setDate(billDate.getDate() + 7)
      break
    case "Monthly":
      billDate.setMonth(billDate.getMonth() + 1)
      break
    case "Quarterly":
      billDate.setMonth(billDate.getMonth() + 3)
      break
    case "Annually":
      billDate.setFullYear(billDate.getFullYear() + 1)
      break
    default:
      // Default to monthly
      billDate.setMonth(billDate.getMonth() + 1)
      break
  }

  return billDate
}

/**
 * Get the billing period options for dropdowns
 */
export const getBillingPeriodOptions = () => [
  { label: "Weekly", value: "Weekly" as BillingPeriod },
  { label: "Monthly", value: "Monthly" as BillingPeriod },
  { label: "Quarterly", value: "Quarterly" as BillingPeriod },
  { label: "Annually", value: "Annually" as BillingPeriod },
]

/**
 * Format billing period for display
 */
export const formatBillingPeriod = (period: BillingPeriod): string => {
  switch (period) {
    case "Weekly":
      return "Weekly"
    case "Monthly":
      return "Monthly"
    case "Quarterly":
      return "Every 3 months"
    case "Annually":
      return "Yearly"
    default:
      return "Monthly"
  }
}
