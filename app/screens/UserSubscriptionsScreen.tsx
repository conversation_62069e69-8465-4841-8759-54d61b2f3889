import React, { FC, useCallback, useEffect } from "react"
import { observer } from "mobx-react-lite"
import { Pressable, View, ViewStyle } from "react-native"
import { AppStackScreenProps, navigate } from "app/navigators"
import { Screen, SubscriptionEmptyState, SubscriptionsList } from "app/components"
import { FontAwesomeIcon } from "@fortawesome/react-native-fontawesome"
import { faPlus } from "@fortawesome/free-solid-svg-icons"
import { colors } from "app/theme"
import { useStores } from "app/models"
import { useAuth } from "app/contexts/AuthContext"
import { UserSubscription } from "app/models/UserSubscription/UserSubscription"
import { useFocusEffect } from "@react-navigation/native"

interface UserSubscriptionsScreenProps extends AppStackScreenProps<"UserSubscriptions"> {}

export const UserSubscriptionsScreen: FC<UserSubscriptionsScreenProps> = observer(function UserSubscriptionsScreen() {
  const rootStore = useStores()
  const { userId } = useAuth()

  // Fetch subscriptions on mount and when userId changes
  useEffect(() => {
    if (userId) {
      // Only fetch if cache is invalid or no data exists
      if (!rootStore.isSubscriptionsCacheValid || rootStore.subscriptions.length === 0) {
        rootStore.fetchUserSubscriptions(userId)
      }
    }
  }, [userId, rootStore.isSubscriptionsCacheValid])

  // Handle screen focus - refresh if cache has been busted
  useFocusEffect(
    useCallback(() => {
      if (userId && !rootStore.isSubscriptionsCacheValid) {
        if (__DEV__ && console.tron) {
          console.tron.log('📋 Screen focused with invalid cache, refreshing subscriptions')
        }
        rootStore.fetchUserSubscriptions(userId)
      }
    }, [userId, rootStore.isSubscriptionsCacheValid])
  )

  // Handle manual refresh (pull-to-refresh)
  const handleRefresh = useCallback(() => {
    if (userId) {
      rootStore.fetchUserSubscriptions(userId, true) // Force refresh
    }
  }, [userId])

  const handleSubscriptionPress = useCallback((subscription: UserSubscription) => {
    if (__DEV__ && console.tron) {
      console.tron.log('📋 Subscription pressed:', subscription.merchantName)
    }
  }, [])

  const handleAddSubscription = useCallback(() => {
    navigate("AddSubscription")
  }, [])

  return (
    <View style={$container}>
      <Screen style={$root} preset="fixed">
        <SubscriptionsList
          sections={rootStore.groupedSubscriptions}
          loading={rootStore.subscriptionsLoading}
          onRefresh={handleRefresh}
          onSubscriptionPress={handleSubscriptionPress}
          ListEmptyComponent={
            <SubscriptionEmptyState onAddSubscription={handleAddSubscription} />
          }
        />

        {rootStore.subscriptions?.length > 0 && (
          <Pressable
            style={$addButton}
            onPress={handleAddSubscription}
          >
            <FontAwesomeIcon icon={faPlus} color="white" size={16} />
          </Pressable>
        )}
      </Screen>
    </View>
  )
})

const $container: ViewStyle = {
  flex: 1,
  backgroundColor: colors.background,
}

const $root: ViewStyle = {
  flex: 1,
}



const $addButton: ViewStyle = {
  position: 'absolute',
  bottom: 80,
  right: 20,
  flexDirection: 'row',
  alignItems: 'center',
  backgroundColor: colors.tint,
  paddingVertical: 12,
  paddingHorizontal: 16,
  borderRadius: 24,
  shadowColor: '#000',
  shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.2,
  shadowRadius: 4,
  elevation: 3,
}
