import React, { FC, useEffect, useState } from "react"
import { ActivityIndicator, FlatList, Image, Pressable, StyleSheet, Text, View } from "react-native"
import { AppStackScreenProps } from "app/navigators"
import { colors, spacing } from "app/theme"
import { observer } from "mobx-react-lite"
import { MaterialCommunityIcons } from "@expo/vector-icons"
import {
  fetchMerchantGroups,
  fetchMerchants,
  fetchPricingTiers,
  fetchProducts,
} from "app/services/ProductService"
import { FontAwesomeIcon } from "@fortawesome/react-native-fontawesome"
import { faArrowLeft, faChevronRight } from "@fortawesome/free-solid-svg-icons"
import { ServiceGroupSearchBar } from "app/components/ServiceGroupSearchBar"
import { ManualAddButton } from "app/components/ManualAddButton"

interface ServiceSelectionScreenProps extends AppStackScreenProps<"ServiceSelection"> {
  onSelectService?: (service: any) => void
}

type SelectionStep = "group" | "merchant" | "product" | "pricing"

interface MerchantGroup {
  id: string
  name: string
  description: string | null
  logo?: { url: string } | null
}

interface Merchant {
  id: string
  name: string
  merchant_group_id: string
  merchant_group?: {
    id: string
    name: string
    logos?: { id: string, url: string }[] | null
  }
}

interface Product {
  id: string
  name: string
  description: string
  merchant_id?: string // Optional now due to database schema change
  merchant?: {
    id: string
    name: string
    merchant_group?: {
      id: string
      name: string
      logos?: { id: string, url: string }[] | null
    }
  }
}

interface PricingTier {
  id: string
  product_id: string
  name: string
  cost: number
  currency: string
  description: string | null
}

// Default icon for services not listed above
const DEFAULT_ICON = "help-circle"

export const ServiceSelectionScreen: FC<ServiceSelectionScreenProps> = observer(
  function ServiceSelectionScreen({ navigation, route }) {
    const [currentStep, setCurrentStep] = useState<SelectionStep>("group")
    const [loading, setLoading] = useState(true)
    const [error, setError] = useState<string | null>(null)

    const [merchantGroups, setMerchantGroups] = useState<MerchantGroup[]>([])
    const [filteredMerchantGroups, setFilteredMerchantGroups] = useState<MerchantGroup[]>([])
    const [merchants, setMerchants] = useState<Merchant[]>([])
    const [products, setProducts] = useState<Product[]>([])
    const [pricingTiers, setPricingTiers] = useState<PricingTier[]>([])

    const [selectedGroup, setSelectedGroup] = useState<MerchantGroup | null>(null)
    const [selectedMerchant, setSelectedMerchant] = useState<Merchant | null>(null)
    const [selectedProduct, setSelectedProduct] = useState<Product | null>(null)
    const [selectedPricingTier, setSelectedPricingTier] = useState<PricingTier | null>(null)

    const [logoURL, setLogoURL] = useState<string | null>(null)

    // Load merchant groups on component mount
    useEffect(() => {
      const loadInitialData = async () => {
        setLoading(true)
        setError(null)
        try {
          const groupsData = await fetchMerchantGroups()
          setMerchantGroups(groupsData)
          setFilteredMerchantGroups(groupsData)
        } catch (err) {
          console.error("Error loading merchant groups:", err)
          setError("Failed to load data. Please try again.")
        } finally {
          setLoading(false)
        }
      }

      loadInitialData()
    }, [])

    // Load merchants when a group is selected
    useEffect(() => {
      if (selectedGroup) {
        const loadMerchants = async () => {
          setLoading(true)
          try {
            const merchantsData = await fetchMerchants(selectedGroup.id)
            setMerchants(merchantsData)
            setLogoURL(selectedGroup.logo?.url || null)
          } catch (err) {
            console.error("Error loading merchants:", err)
            setError("Failed to load merchants. Please try again.")
          } finally {
            setLoading(false)
          }
        }
        loadMerchants()
      }
    }, [selectedGroup])

    // Load products when a merchant is selected
    useEffect(() => {
      if (selectedMerchant) {
        const loadProducts = async () => {
          setLoading(true)
          try {
            const productsData = await fetchProducts(selectedMerchant.id)
            setProducts(productsData)
          } catch (err) {
            console.error("Error loading products:", err)
            setError("Failed to load products. Please try again.")
          } finally {
            setLoading(false)
          }
        }

        loadProducts()
      }
    }, [selectedMerchant])

    // Load pricing tiers when a product is selected
    useEffect(() => {
      if (selectedProduct) {
        const loadPricingTiers = async () => {
          setLoading(true)
          try {
            const pricingTiersData = await fetchPricingTiers(selectedProduct.id)
            setPricingTiers(pricingTiersData)
          } catch (err) {
            console.error("Error loading pricing tiers:", err)
            setError("Failed to load pricing options. Please try again.")
          } finally {
            setLoading(false)
          }
        }

        loadPricingTiers()
      }
    }, [selectedProduct])

    const handleSearchResults = (results: MerchantGroup[]) => {
      setFilteredMerchantGroups(results)
    }

    const handleManualAdd = () => {
      // Placeholder function for future implementation
      console.log("Manual add button pressed")
    }

    const handleGroupSelection = (group: MerchantGroup) => {
      setSelectedGroup(group)
      setCurrentStep("merchant")
    }

    const handleMerchantSelection = (merchant: Merchant) => {
      setSelectedMerchant(merchant)
      setCurrentStep("product")
    }

    const handleProductSelection = (product: Product) => {
      setSelectedProduct(product)
      setCurrentStep("pricing")
    }

    const handlePricingTierSelection = (pricingTier: PricingTier) => {
      setSelectedPricingTier(pricingTier);

      if (route.params?.onSelectService && selectedProduct && pricingTier) {
        // Pass complete information about the pricing tier and product
        const serviceInfo = {
          name: `${selectedProduct.name}`,
          pricingTierId: pricingTier.id,
          description: pricingTier.description || selectedProduct.description,
          cost: pricingTier.cost,
          currency: pricingTier.currency,
          productId: selectedProduct.id,
          merchantGroupLogo: logoURL
        };
        console.log('Passing service info to AddSubscriptionScreen:', serviceInfo);
        route.params.onSelectService(serviceInfo);
        // Navigate back immediately after selection
        navigation.goBack();
      }
    }

    const handleServiceCardPress = () => {
      // Reset all selections and start from the beginning
      setSelectedGroup(null);
      setSelectedMerchant(null);
      setSelectedProduct(null);
      setSelectedPricingTier(null);
      setCurrentStep("group");
    }

    const goBack = () => {
      if (currentStep === "merchant") {
        setSelectedGroup(null)
        setCurrentStep("group")
      } else if (currentStep === "product") {
        setSelectedMerchant(null)
        setCurrentStep("merchant")
      } else if (currentStep === "pricing") {
        setSelectedProduct(null)
        setCurrentStep("product")
      } else {
        navigation.goBack()
      }
    }

    const renderHeader = () => {
      let title = "Select a Service Provider Group"
      if (currentStep === "merchant") title = `Select a Service Provider from ${selectedGroup?.name}`
      if (currentStep === "product") title = `Select a Service from ${selectedMerchant?.name}`
      if (currentStep === "pricing") title = `Select a Pricing Option for ${selectedProduct?.name}`

      return (
        <View style={styles.header}>
          <Pressable style={styles.backButton} onPress={goBack}>
            <FontAwesomeIcon icon={faArrowLeft} size={20} color={colors.text} />
          </Pressable>
          <Text style={styles.title}>{title}</Text>
        </View>
      )
    }

    const renderSearchSection = () => {
      if (currentStep !== "group") return null

      return (
        <View style={styles.searchSection}>
          <View style={styles.searchContainer}>
            <ServiceGroupSearchBar
              data={merchantGroups}
              onSearchResults={handleSearchResults}
              placeholder="Apple..."
              style={styles.searchBar}
            />
          </View>
          <ManualAddButton
            text="Manual Add"
            onPress={handleManualAdd}
            style={styles.manualAddButton}
          />
        </View>
      )
    }

    const renderGroupItem = ({ item }: { item: MerchantGroup }) => (
      <Pressable
        style={styles.serviceItem}
        onPress={() => handleGroupSelection(item)}
      >
        <View style={styles.serviceContent}>
          {item.logo?.url ? (
            <Image source={{ uri: item.logo.url }} style={styles.logoImage} />
          ) : (
            <MaterialCommunityIcons
              name={DEFAULT_ICON as keyof typeof MaterialCommunityIcons.glyphMap}
              size={24}
              color={colors.tint}
              style={styles.serviceIcon}
            />
          )}
          <View style={styles.textContainer}>
            <Text style={styles.serviceText}>{item.name}</Text>
            {item.description && (
              <Text style={styles.descriptionText} numberOfLines={2}>
                {item.description}
              </Text>
            )}
          </View>
          <FontAwesomeIcon icon={faChevronRight} size={16} color={colors.palette.neutral500} />
        </View>
      </Pressable>
    )

    const renderMerchantItem = ({ item }: { item: Merchant }) => (
      <Pressable
        style={styles.serviceItem}
        onPress={() => handleMerchantSelection(item)}
      >
        <View style={styles.serviceContent}>
          {logoURL ? (
            <Image source={{ uri: logoURL }} style={styles.logoImage} />
          ) : (
            <MaterialCommunityIcons
              name={DEFAULT_ICON as keyof typeof MaterialCommunityIcons.glyphMap}
              size={24}
              color={colors.tint}
              style={styles.serviceIcon}
            />
          )}
          <Text style={styles.serviceText}>{item.name}</Text>
          <FontAwesomeIcon icon={faChevronRight} size={16} color={colors.palette.neutral500} />
        </View>
      </Pressable>
    )

    const renderProductItem = ({ item }: { item: Product }) => (
      <Pressable
        style={styles.serviceItem}
        onPress={() => handleProductSelection(item)}
      >
        <View style={styles.serviceContent}>
          {logoURL ? (
            <Image source={{ uri: logoURL }} style={styles.logoImage} />
          ) : (
            <MaterialCommunityIcons
              name={DEFAULT_ICON as keyof typeof MaterialCommunityIcons.glyphMap}
              size={24}
              color={colors.tint}
              style={styles.serviceIcon}
            />
          )}
          <View style={styles.textContainer}>
            <Text style={styles.serviceText}>{item.name}</Text>
            <Text style={styles.descriptionText} numberOfLines={2}>
              {item.description}
            </Text>
          </View>
          <FontAwesomeIcon icon={faChevronRight} size={16} color={colors.palette.neutral500} />
        </View>
      </Pressable>
    )

    // Helper function to get the correct currency icon
    const getCurrencyIcon = (currency: string): keyof typeof MaterialCommunityIcons.glyphMap => {
      switch (currency) {
        case "EUR":
          return "currency-eur";
        case "GBP":
          return "currency-gbp";
        case "USD":
        default:
          return "currency-usd";
      }
    }

    const renderPricingTierItem = ({ item }: { item: PricingTier }) => (
      <Pressable
        style={[styles.serviceItem, selectedPricingTier?.id === item.id && styles.selectedItem]}
        onPress={() => handlePricingTierSelection(item)}
      >
        <View style={styles.serviceContent}>
          <MaterialCommunityIcons
            name={getCurrencyIcon(item.currency) as keyof typeof MaterialCommunityIcons.glyphMap}
            size={24}
            color={colors.tint}
            style={styles.serviceIcon}
          />
          <View style={styles.textContainer}>
            <Text style={styles.serviceText}>{item.name}</Text>
            <Text style={styles.priceText}>
              {item.currency} {item.cost.toFixed(2)}
            </Text>
            {item.description && (
              <Text style={styles.descriptionText} numberOfLines={2}>
                {item.description}
              </Text>
            )}
          </View>
        </View>
      </Pressable>
    )

    if (loading) {
      return (
        <View style={[styles.container, styles.centerContent]}>
          <ActivityIndicator size="large" color={colors.tint} />
          <Text style={styles.loadingText}>Loading services...</Text>
        </View>
      )
    }

    if (error) {
      return (
        <View style={[styles.container, styles.centerContent]}>
          <Text style={styles.errorText}>{error}</Text>
          <Pressable style={styles.retryButton} onPress={() => navigation.replace("ServiceSelection")}>
            <Text style={styles.retryButtonText}>Retry</Text>
          </Pressable>
        </View>
      )
    }

    return (
      <View style={styles.container}>
        {renderHeader()}
        {renderSearchSection()}

        {currentStep === "group" && (
          <FlatList
            data={filteredMerchantGroups}
            keyExtractor={(item) => item.id}
            renderItem={renderGroupItem}
            ItemSeparatorComponent={() => <View style={styles.separator} />}
            contentContainerStyle={styles.listContent}
            ListEmptyComponent={
              <Text style={styles.emptyText}>No service provider groups found</Text>
            }
          />
        )}

        {currentStep === "merchant" && (
          <FlatList
            data={merchants}
            keyExtractor={(item) => item.id}
            renderItem={renderMerchantItem}
            ItemSeparatorComponent={() => <View style={styles.separator} />}
            contentContainerStyle={styles.listContent}
            ListEmptyComponent={
              <Text style={styles.emptyText}>No service providers found in this group</Text>
            }
          />
        )}

        {currentStep === "product" && (
          <FlatList
            data={products}
            keyExtractor={(item) => item.id}
            renderItem={renderProductItem}
            ItemSeparatorComponent={() => <View style={styles.separator} />}
            contentContainerStyle={styles.listContent}
            ListEmptyComponent={
              <Text style={styles.emptyText}>No services found for this provider</Text>
            }
          />
        )}

        {currentStep === "pricing" && (
          <FlatList
            data={pricingTiers}
            keyExtractor={(item) => item.id}
            renderItem={renderPricingTierItem}
            ItemSeparatorComponent={() => <View style={styles.separator} />}
            contentContainerStyle={styles.listContent}
            ListEmptyComponent={
              <Text style={styles.emptyText}>No pricing options found for this service</Text>
            }
          />
        )}
      </View>
    )
  }
)

const styles = StyleSheet.create({
  backButton: {
    padding: spacing.xs,
    marginRight: spacing.sm
  },
  centerContent: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: spacing.lg
  },
  container: {
    flex: 1,
    backgroundColor: colors.background
  },
  descriptionText: {
    fontSize: 14,
    color: colors.palette.neutral600,
    marginTop: spacing.xxs
  },
  emptyText: {
    textAlign: 'center',
    padding: spacing.xl,
    color: colors.palette.neutral500,
    fontStyle: 'italic'
  },
  errorText: {
    color: colors.error,
    fontSize: 16,
    textAlign: 'center',
    marginBottom: spacing.lg
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border
  },
  listContent: {
    padding: spacing.sm
  },
  loadingText: {
    marginTop: spacing.md,
    color: colors.text,
    fontSize: 16
  },
  logoImage: {
    width: 40,
    height: 40,
    borderRadius: 8,
    marginRight: spacing.md
  },
  priceText: {
    fontSize: 15,
    fontWeight: '500',
    color: colors.palette.accent500,
    marginTop: spacing.xxs
  },
  retryButton: {
    backgroundColor: colors.tint,
    paddingVertical: spacing.sm,
    paddingHorizontal: spacing.lg,
    borderRadius: 8
  },
  retryButtonText: {
    color: colors.palette.neutral100,
    fontWeight: 'bold'
  },
  separator: {
    height: 1,
    backgroundColor: colors.border,
    marginVertical: spacing.xs
  },
  serviceContent: {
    flexDirection: "row",
    alignItems: "center"
  },
  serviceIcon: {
    marginRight: spacing.md,
    width: 24
  },
  serviceItem: {
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.sm,
    borderRadius: 8,
    marginBottom: spacing.xs
  },
  selectedItem: {
    backgroundColor: colors.palette.primary100,
    borderWidth: 1,
    borderColor: colors.palette.primary600
  },
  serviceText: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.text
  },
  textContainer: {
    flex: 1,
    marginRight: spacing.sm
  },
  title: {
    fontSize: 18,
    fontWeight: "bold",
    color: colors.text,
    flex: 1
  },
  serviceSelectionContainer: {
    padding: spacing.md
  },
  serviceSelectionCard: {
    marginBottom: spacing.sm
  },
  cardFooter: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: spacing.sm
  },
  cardLogo: {
    width: 40,
    height: 40,
    borderRadius: 8,
    marginRight: spacing.md
  },
  cardFooterText: {
    flex: 1
  },
  cardPrice: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.palette.primary600,
    marginBottom: spacing.xxs
  },
  cardDescription: {
    fontSize: 14,
    color: colors.palette.neutral600
  },
  searchSection: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    marginBottom: spacing.sm,
    backgroundColor: colors.background
  },
  searchContainer: {
    flex: 1,
    marginRight: spacing.sm
  },
  searchBar: {
    paddingHorizontal: 0,
    paddingVertical: 0
  },
  manualAddButton: {
    minWidth: 100,
    paddingHorizontal: spacing.sm
  }
})
