import React, { FC, useState, useEffect } from "react"
import { observer } from "mobx-react-lite"
import { View, StyleSheet, ScrollView, Alert } from "react-native"
import { AppStackScreenProps } from "app/navigators"
import { Screen, Text, TextField, DatePickerField, LoadingButton } from "app/components"
import { colors, spacing, typography } from "app/theme"
import { useStores } from "app/models"
import { useAuth } from "app/contexts/AuthContext"
import { manualAddService, CreateServiceProviderData } from "app/services/ManualAddService"
import { subscriptionService } from "app/services/SubscriptionService"
import { ErrorType, ErrorExperience, reportSentryError } from "app/utils/crashReporting"
import { 
  BillingPeriod, 
  calculateSmartBillDate, 
  getBillingPeriodOptions 
} from "app/utils/billDateCalculator"
import { Picker } from "@react-native-picker/picker"

interface ManualAddFormScreenProps extends AppStackScreenProps<"ManualAddForm"> {}

interface FormData {
  serviceProviderGroupName: string
  merchantName: string
  merchantCategory: string
  productName: string
  productDescription: string
  pricingTierName: string
  cost: string
  currency: string
  billingPeriod: BillingPeriod
  trialPeriodDays: string
  startDate: Date
  billDate: Date
}

const MERCHANT_CATEGORIES = [
  "Streaming",
  "Software",
  "Gaming",
  "Fitness",
  "Education",
  "News & Media",
  "Music",
  "Cloud Storage",
  "Productivity",
  "Entertainment",
  "Other"
]

const CURRENCIES = [
  { label: "EUR (€)", value: "EUR" },
  { label: "USD ($)", value: "USD" },
  { label: "GBP (£)", value: "GBP" },
]

export const ManualAddFormScreen: FC<ManualAddFormScreenProps> = observer(
  function ManualAddFormScreen({ navigation, route }) {
    const rootStore = useStores()
    const { userId } = useAuth()
    const [isLoading, setIsLoading] = useState(false)
    const [formData, setFormData] = useState<FormData>({
      serviceProviderGroupName: route.params?.serviceProviderGroupName || "",
      merchantName: "",
      merchantCategory: "Other",
      productName: "",
      productDescription: "",
      pricingTierName: "Standard",
      cost: "",
      currency: "EUR",
      billingPeriod: "Monthly",
      trialPeriodDays: "",
      startDate: new Date(),
      billDate: new Date(),
    })

    // Update bill date when start date or billing period changes
    useEffect(() => {
      const smartBillDate = calculateSmartBillDate(formData.startDate, formData.billingPeriod)
      setFormData(prev => ({ ...prev, billDate: smartBillDate }))
    }, [formData.startDate, formData.billingPeriod])

    const updateFormField = <K extends keyof FormData>(field: K, value: FormData[K]) => {
      setFormData(prev => ({ ...prev, [field]: value }))
    }

    const validateForm = (): string | null => {
      if (!formData.serviceProviderGroupName.trim()) {
        return "Service Provider Group name is required"
      }
      if (!formData.merchantName.trim()) {
        return "Merchant/Service name is required"
      }
      if (!formData.productName.trim()) {
        return "Product/Service name is required"
      }
      if (!formData.cost.trim() || isNaN(parseFloat(formData.cost))) {
        return "Valid cost is required"
      }
      if (parseFloat(formData.cost) < 0) {
        return "Cost must be positive"
      }
      if (formData.trialPeriodDays.trim() && isNaN(parseInt(formData.trialPeriodDays))) {
        return "Trial period must be a valid number"
      }
      return null
    }

    const handleSave = async () => {
      if (!userId) {
        Alert.alert('Error', 'User not authenticated')
        return
      }

      const validationError = validateForm()
      if (validationError) {
        Alert.alert('Validation Error', validationError)
        return
      }

      setIsLoading(true)

      try {
        if (__DEV__ && console.tron) {
          console.tron.log('🏗️ Creating manual service provider:', formData)
        }

        // Prepare service provider data
        const serviceProviderData: CreateServiceProviderData = {
          merchantGroupName: formData.serviceProviderGroupName.trim(),
          merchantName: formData.merchantName.trim(),
          merchantCategory: formData.merchantCategory,
          productName: formData.productName.trim(),
          productDescription: formData.productDescription.trim() || `${formData.productName} service`,
          pricingTierName: formData.pricingTierName.trim(),
          cost: parseFloat(formData.cost),
          currency: formData.currency,
          billingPeriod: formData.billingPeriod,
          trialPeriodDays: formData.trialPeriodDays.trim() ? parseInt(formData.trialPeriodDays) : undefined,
        }

        // Create the complete service provider
        const serviceResult = await manualAddService.createCompleteServiceProvider(serviceProviderData)
        
        if (!serviceResult.success || !serviceResult.data) {
          throw new Error(serviceResult.error || 'Failed to create service provider')
        }

        // Create the user subscription
        const subscriptionResult = await subscriptionService.createUserSubscription({
          userId,
          profileType: 'user',
          productId: serviceResult.data.productId,
          startedOn: formData.startDate.toISOString(),
          billDate: formData.billDate.toISOString(),
          pricingTierId: serviceResult.data.pricingTierId,
        })

        if (!subscriptionResult.success || !subscriptionResult.data) {
          throw new Error(subscriptionResult.error || 'Failed to create subscription')
        }

        // Add the new subscription to the store immediately for instant UI update
        await rootStore.addSubscriptionToStore(subscriptionResult.data)

        // Bust cache to ensure fresh data on next fetch
        rootStore.bustSubscriptionsCache()

        if (__DEV__ && console.tron) {
          console.tron.log('✅ Successfully created manual subscription:', subscriptionResult.data.id)
        }

        // Navigate back to the main screen
        navigation.navigate("Home")
        
      } catch (error) {
        const subscriptionError = error instanceof Error ? error : new Error('Failed to create subscription')
        reportSentryError(subscriptionError, ErrorType.HANDLED, ErrorExperience.DataWrite)

        if (__DEV__ && console.tron) {
          console.tron.error('❌ Failed to create manual subscription:', error)
        }

        Alert.alert(
          'Error',
          'Failed to create subscription. Please try again.',
          [{ text: 'OK' }]
        )
      } finally {
        setIsLoading(false)
      }
    }

    return (
      <Screen preset="fixed" safeAreaEdges={["top"]} style={styles.screen}>
        <ScrollView
          contentContainerStyle={styles.scrollContainer}
          keyboardShouldPersistTaps="handled"
        >
          <Text style={styles.title}>Create New Service</Text>
          <Text style={styles.subtitle}>
            Fill in the details for your new subscription service
          </Text>

          {/* Service Provider Group Section */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Service Provider Group</Text>
            <TextField
              label="Service Provider Group Name"
              value={formData.serviceProviderGroupName}
              onChangeText={(text) => updateFormField('serviceProviderGroupName', text)}
              placeholder="e.g., Netflix, Spotify, Adobe"
              editable={false}
              style={styles.disabledField}
            />
          </View>

          {/* Merchant Details Section */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Service Details</Text>
            
            <TextField
              label="Individual Merchant/Service Name"
              value={formData.merchantName}
              onChangeText={(text) => updateFormField('merchantName', text)}
              placeholder="e.g., Netflix US, Spotify Premium"
            />

            <View style={styles.pickerContainer}>
              <Text style={styles.pickerLabel}>Category</Text>
              <Picker
                selectedValue={formData.merchantCategory}
                onValueChange={(value) => updateFormField('merchantCategory', value)}
                style={styles.picker}
              >
                {MERCHANT_CATEGORIES.map((category) => (
                  <Picker.Item key={category} label={category} value={category} />
                ))}
              </Picker>
            </View>

            <TextField
              label="Product/Service Name"
              value={formData.productName}
              onChangeText={(text) => updateFormField('productName', text)}
              placeholder="e.g., Premium Plan, Pro Subscription"
            />

            <TextField
              label="Description (Optional)"
              value={formData.productDescription}
              onChangeText={(text) => updateFormField('productDescription', text)}
              placeholder="Brief description of the service"
              multiline
              numberOfLines={3}
            />
          </View>

          {/* Pricing Section */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Pricing Details</Text>
            
            <TextField
              label="Pricing Tier Name"
              value={formData.pricingTierName}
              onChangeText={(text) => updateFormField('pricingTierName', text)}
              placeholder="e.g., Standard, Premium, Pro"
            />

            <View style={styles.row}>
              <View style={styles.costContainer}>
                <TextField
                  label="Cost"
                  value={formData.cost}
                  onChangeText={(text) => updateFormField('cost', text)}
                  placeholder="9.99"
                  keyboardType="decimal-pad"
                />
              </View>
              
              <View style={styles.currencyContainer}>
                <Text style={styles.pickerLabel}>Currency</Text>
                <Picker
                  selectedValue={formData.currency}
                  onValueChange={(value) => updateFormField('currency', value)}
                  style={styles.picker}
                >
                  {CURRENCIES.map((currency) => (
                    <Picker.Item key={currency.value} label={currency.label} value={currency.value} />
                  ))}
                </Picker>
              </View>
            </View>

            <View style={styles.pickerContainer}>
              <Text style={styles.pickerLabel}>Billing Frequency</Text>
              <Picker
                selectedValue={formData.billingPeriod}
                onValueChange={(value) => updateFormField('billingPeriod', value)}
                style={styles.picker}
              >
                {getBillingPeriodOptions().map((option) => (
                  <Picker.Item key={option.value} label={option.label} value={option.value} />
                ))}
              </Picker>
            </View>

            <TextField
              label="Trial Period (Days, Optional)"
              value={formData.trialPeriodDays}
              onChangeText={(text) => updateFormField('trialPeriodDays', text)}
              placeholder="e.g., 7, 14, 30"
              keyboardType="number-pad"
            />
          </View>

          {/* Subscription Details Section */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Your Subscription Details</Text>

            <DatePickerField
              label="Start Date"
              value={formData.startDate}
              onChange={(date) => updateFormField('startDate', date)}
              style={styles.dateField}
            />

            <DatePickerField
              label="Bill Date"
              value={formData.billDate}
              onChange={(date) => updateFormField('billDate', date)}
              style={styles.dateField}
            />
          </View>

          <LoadingButton
            title="Create Subscription"
            onPress={handleSave}
            loading={isLoading}
            loadingText="Creating..."
            style={styles.saveButton}
          />

          <View style={{ height: 80 }} />
        </ScrollView>
      </Screen>
    )
  }
)

const styles = StyleSheet.create({
  screen: {
    backgroundColor: colors.background,
  },
  scrollContainer: {
    padding: spacing.lg,
  },
  title: {
    fontSize: 24,
    fontFamily: typography.primary.bold,
    color: colors.text,
    marginBottom: spacing.xs,
  },
  subtitle: {
    fontSize: 16,
    fontFamily: typography.primary.normal,
    color: colors.textDim,
    marginBottom: spacing.lg,
    lineHeight: 22,
  },
  section: {
    marginBottom: spacing.lg,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: typography.primary.medium,
    color: colors.text,
    marginBottom: spacing.md,
  },
  disabledField: {
    opacity: 0.6,
  },
  pickerContainer: {
    marginBottom: spacing.md,
  },
  pickerLabel: {
    fontSize: 16,
    fontFamily: typography.primary.medium,
    color: colors.text,
    marginBottom: spacing.xs,
  },
  picker: {
    backgroundColor: colors.palette.neutral50,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: colors.palette.neutral300,
  },
  row: {
    flexDirection: "row",
    gap: spacing.md,
  },
  costContainer: {
    flex: 2,
  },
  currencyContainer: {
    flex: 1,
  },
  dateField: {
    marginBottom: spacing.md,
  },
  saveButton: {
    marginTop: spacing.lg,
  },
})
