import React, { FC, useState, useEffect } from "react"
import { observer } from "mobx-react-lite"
import { View, StyleSheet, FlatList, Pressable } from "react-native"
import { AppStackScreenProps } from "app/navigators"
import { Screen, Text, TextField } from "app/components"
import { ConfirmationModal } from "app/components/ConfirmationModal"
import { colors, spacing, typography } from "app/theme"
import { useStores } from "app/models"
import { fuzzy } from "fast-fuzzy"
import { FontAwesomeIcon } from "@fortawesome/react-native-fontawesome"
import { faChevronRight, faPlus } from "@fortawesome/free-solid-svg-icons"

interface ManualAddSearchScreenProps extends AppStackScreenProps<"ManualAddSearch"> {}

interface MerchantGroup {
  id: string
  name: string
  description: string | null
}

const SEARCH_OPTIONS = {
  threshold: 0.3,
  ignoreCase: true,
  normalizeWhitespace: true,
  ignoreSymbols: true
}

export const ManualAddSearchScreen: FC<ManualAddSearchScreenProps> = observer(
  function ManualAddSearchScreen({ navigation }) {
    const rootStore = useStores()
    const [searchQuery, setSearchQuery] = useState("")
    const [suggestions, setSuggestions] = useState<MerchantGroup[]>([])
    const [showConfirmation, setShowConfirmation] = useState(false)
    const [merchantGroups, setMerchantGroups] = useState<MerchantGroup[]>([])

    // Load merchant groups on mount
    useEffect(() => {
      loadMerchantGroups()
    }, [])

    // Update suggestions when search query changes
    useEffect(() => {
      if (searchQuery.trim().length > 0) {
        updateSuggestions(searchQuery)
      } else {
        setSuggestions([])
      }
    }, [searchQuery, merchantGroups])

    const loadMerchantGroups = async () => {
      try {
        // Use existing data from store if available
        if (rootStore.merchantGroups.length > 0) {
          setMerchantGroups(rootStore.merchantGroups.slice())
        } else {
          // Load from API if not in store
          const { fetchMerchantGroups } = await import("app/services/ProductService")
          const groups = await fetchMerchantGroups()
          setMerchantGroups(groups)
        }
      } catch (error) {
        if (__DEV__ && console.tron) {
          console.tron.error('❌ Failed to load merchant groups:', error)
        }
      }
    }

    const updateSuggestions = (query: string) => {
      if (!query.trim()) {
        setSuggestions([])
        return
      }

      // Use fuzzy search to find top 3 closest matches
      const results = fuzzy(query, merchantGroups, {
        ...SEARCH_OPTIONS,
        keySelector: (group) => group.name
      })

      // Take top 3 results
      setSuggestions(results.slice(0, 3))
    }

    const handleSearchChange = (text: string) => {
      setSearchQuery(text)
    }

    const handleSuggestionPress = (suggestion: MerchantGroup) => {
      setSearchQuery(suggestion.name)
      // Navigate directly to form with existing service provider
      navigation.navigate("ManualAddForm", { 
        serviceProviderGroupName: suggestion.name 
      })
    }

    const handleCreateNew = () => {
      if (searchQuery.trim().length === 0) {
        return
      }
      setShowConfirmation(true)
    }

    const handleConfirmCreate = () => {
      setShowConfirmation(false)
      navigation.navigate("ManualAddForm", { 
        serviceProviderGroupName: searchQuery.trim() 
      })
    }

    const handleCancelCreate = () => {
      setShowConfirmation(false)
    }

    const renderSuggestionItem = ({ item }: { item: MerchantGroup }) => (
      <Pressable style={styles.suggestionItem} onPress={() => handleSuggestionPress(item)}>
        <View style={styles.suggestionContent}>
          <Text style={styles.suggestionName}>{item.name}</Text>
          {item.description && (
            <Text style={styles.suggestionDescription}>{item.description}</Text>
          )}
        </View>
        <FontAwesomeIcon
          icon={faChevronRight}
          size={16}
          color={colors.textDim}
        />
      </Pressable>
    )

    const showCreateButton = searchQuery.trim().length > 0 && suggestions.length === 0

    return (
      <Screen preset="fixed" safeAreaEdges={["top"]} style={styles.screen}>
        <View style={styles.container}>
          <Text style={styles.title}>Search Service Provider Group</Text>
          <Text style={styles.subtitle}>
            Search for an existing service provider group or create a new one
          </Text>

          <TextField
            value={searchQuery}
            onChangeText={handleSearchChange}
            placeholder="e.g., Netflix, Spotify, Adobe..."
            style={styles.searchInput}
            autoFocus
          />

          {suggestions.length > 0 && (
            <View style={styles.suggestionsSection}>
              <Text style={styles.sectionTitle}>Existing Service Provider Groups</Text>
              <FlatList
                data={suggestions}
                keyExtractor={(item) => item.id}
                renderItem={renderSuggestionItem}
                ItemSeparatorComponent={() => <View style={styles.separator} />}
                style={styles.suggestionsList}
              />
            </View>
          )}

          {showCreateButton && (
            <View style={styles.createSection}>
              <Text style={styles.sectionTitle}>Create New</Text>
              <Pressable style={styles.createButton} onPress={handleCreateNew}>
                <FontAwesomeIcon
                  icon={faPlus}
                  size={20}
                  color={colors.palette.primary500}
                />
                <View style={styles.createButtonContent}>
                  <Text style={styles.createButtonText}>
                    Create "{searchQuery}"
                  </Text>
                  <Text style={styles.createButtonSubtext}>
                    This service provider group doesn't exist yet
                  </Text>
                </View>
                <FontAwesomeIcon
                  icon={faChevronRight}
                  size={16}
                  color={colors.textDim}
                />
              </Pressable>
            </View>
          )}
        </View>

        <ConfirmationModal
          visible={showConfirmation}
          title="Create New Service Provider Group"
          message={`This service provider group "${searchQuery}" doesn't exist yet. Would you like to create it?`}
          confirmText="Create"
          cancelText="Cancel"
          onConfirm={handleConfirmCreate}
          onCancel={handleCancelCreate}
        />
      </Screen>
    )
  }
)

const styles = StyleSheet.create({
  screen: {
    backgroundColor: colors.background,
  },
  container: {
    flex: 1,
    padding: spacing.lg,
  },
  title: {
    fontSize: 24,
    fontFamily: typography.primary.bold,
    color: colors.text,
    marginBottom: spacing.xs,
  },
  subtitle: {
    fontSize: 16,
    fontFamily: typography.primary.normal,
    color: colors.textDim,
    marginBottom: spacing.lg,
    lineHeight: 22,
  },
  searchInput: {
    marginBottom: spacing.lg,
  },
  suggestionsSection: {
    marginBottom: spacing.lg,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: typography.primary.medium,
    color: colors.text,
    marginBottom: spacing.md,
  },
  suggestionsList: {
    maxHeight: 300,
  },
  suggestionItem: {
    flexDirection: "row",
    alignItems: "center",
    padding: spacing.md,
    backgroundColor: colors.palette.neutral50,
    borderRadius: 8,
  },
  suggestionContent: {
    flex: 1,
  },
  suggestionName: {
    fontSize: 16,
    fontFamily: typography.primary.medium,
    color: colors.text,
  },
  suggestionDescription: {
    fontSize: 14,
    fontFamily: typography.primary.normal,
    color: colors.textDim,
    marginTop: spacing.xs,
  },
  separator: {
    height: spacing.xs,
  },
  createSection: {
    marginTop: spacing.lg,
  },
  createButton: {
    flexDirection: "row",
    alignItems: "center",
    padding: spacing.md,
    backgroundColor: colors.palette.primary100,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: colors.palette.primary200,
  },
  createButtonContent: {
    flex: 1,
    marginLeft: spacing.md,
  },
  createButtonText: {
    fontSize: 16,
    fontFamily: typography.primary.medium,
    color: colors.palette.primary600,
  },
  createButtonSubtext: {
    fontSize: 14,
    fontFamily: typography.primary.normal,
    color: colors.palette.primary500,
    marginTop: spacing.xs,
  },
})
